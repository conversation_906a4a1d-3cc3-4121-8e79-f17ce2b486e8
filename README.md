# 项目配置说明

## 颜色变量
主要使用两个颜色变量，通过 `t()` 方法调用：

### 主色调 (Primary Color)
```vue
:style="'background: ' + t('color1')"
```
使用场景：
- 重要按钮背景色
- 强调文字颜色
- 佣金金额颜色
- 活跃状态指示

### 副色调 (Secondary Color)
```vue
:style="'background: ' + t('color2')"
```
使用场景：
- 页面背景色
- 次要按钮背景色
- 分割线颜色
- 输入框背景色

### 渐变色使用示例
```vue
:style="'background: linear-gradient(180deg, ' + t('color1') + ', ' + t('color1') + ')'"
```

## 常用跳转路径

### 店铺相关
- 店铺首页: `/pagesExt/business/index?id=${shop_id}`

### 带货团相关
- 带货团列表: `/daihuobiji/kuaituan/tuanzhangtuanlist?bid=${leader_id}`

### 笔记相关
- 笔记详情: `/daihuobiji/detail/index?id=${note_id}`
- 笔记编辑: `/daihuobiji/detail/fatieedit?id=${note_id}&type=edit`

## 图标路径
所有图标都存放在 `static/img/` 目录下：
- 设置图标: `set.png`
- 添加图标: `add.png`
- 关闭图标: `close2.png`

## 音频资源路径
音频文件存放在 `static/MP3/` 目录下：
- 开场音频: `kaichang.mp3`

## 图片资源引用规范
使用云资源时必须加上域名前缀：
```vue
<!-- 正确的图片引用方式 -->
<image :src="pre_url+'/static/img/example.png'" />

<!-- 错误的引用方式 -->
<image src="/static/img/example.png" />
```

### 初始化域名前缀
在页面的 onLoad 生命周期中初始化：
```javascript
onLoad() {
    // 初始化云资源前缀URL
    const app = getApp();
    this.pre_url = app.globalData.pre_url || '';
}
```

### 音频播放规范
使用 uni.createInnerAudioContext 播放音频：
```javascript
// 播放音频
playAudio() {
    if (!this.audioEnabled) return;

    const audio = uni.createInnerAudioContext();
    audio.src = this.pre_url + '/static/MP3/audio.mp3';

    audio.onError((res) => {
        console.error('音频播放失败:', res);
    });

    audio.play();
}
```

## 组件使用说明

### DetailNav 组件
```vue
<DetailNav v-if="detailInfo.id" :userInfo="detailInfo" :noteId="String(detailInfo.id)" />
```
必需参数：
- userInfo: 用户信息对象
- noteId: 笔记ID (字符串类型)

### 全局方法
- goto: 页面跳转
  ```vue
  @tap="goto" :data-url="'/path/to/page?param=value'"
  ```


	<!-- #ifdef !MP-WEIXIN-->
			<view class="content">
				<view class="info-item" @tap="goto" data-url="/pagesB/login/login">
					<view class="t1">切换账号</view>
					<view class="t2"></view>
					<image class="t3" src="/static/img/arrowright.png" />
				</view>
			</view>
			<!-- #endif -->
			<view class="content">
				<view class="info-item" @tap="logout">
					<view class="t1">退出登录</view>
					<view class="t2"></view>
					<image class="t3" src="/static/img/arrowright.png" />
				</view>
			</view>
			<!-- #ifdef APP-PLUS -->
			<view class="content">
				<view class="info-item" @tap="delaccount">
					<view class="t1">注销账号</view>
					<view class="t2"></view>
					<image class="t3" src="/static/img/arrowright.png" />
				</view>
			</view>
			<!-- #endif -->

# 项目界面结构整理

## 舌诊功能模块

### 页面结构
- `pagesB/shezhen/guide.vue` - 舌诊引导页面
- `pagesB/shezhen/photo-upload.vue` - 舌苔拍摄上传页面

### 通用样式规范

#### 1. 颜色方案
- 主色调：#007AFF（iOS蓝）
- 背景色：#ffffff（白色）
- 次要背景：#f8f9fa（浅灰）
- 文字颜色：#333（深灰），#666（中灰）
- 边框颜色：#f0f0f0（浅灰边框）

#### 2. 布局结构
```vue
<template>
  <view class="container">
    <!-- 顶部标题区域 -->
    <view class="header-section">
      <view class="back-btn" @click="goBack">
        <text class="back-icon">‹</text>
      </view>
      <view class="header-content">
        <text class="main-title">页面标题</text>
      </view>
      <view class="help-btn" @click="showHelp">
        <text class="help-icon">❓</text>
      </view>
    </view>
    
    <!-- 主体内容区域 -->
    <view class="content-section">
      <!-- 具体内容 -->
    </view>
    
    <!-- 底部操作区域 -->
    <view class="action-section">
      <!-- 操作按钮 -->
    </view>
  </view>
</template>
```

#### 3. 通用CSS类
```css
/* 主容器 */
.container {
  width: 100vw;
  height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.header-section {
  background: #ffffff;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 按钮样式 */
.back-btn, .help-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f8f9fa;
}

/* 主标题 */
.main-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

/* 圆形指示器 */
.circle-indicator {
  width: 40rpx;
  height: 40rpx;
  background: #007AFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
}

/* 卡片样式 */
.card-item {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}
```

#### 4. 拍摄框规范
- 舌苔拍摄框：竖立长方形
  - 宽度：240rpx
  - 高度：320rpx
  - 边框：3rpx solid #007AFF
  - 圆角：12rpx

#### 5. 日志规范
使用时间戳-级别-[文件名]-[函数名_数字序号]的固定标准格式：
```javascript
console.log('2025-01-26 11:35:00,001-INFO-[文件名][函数名_001] 日志内容');
console.error('2025-01-26 11:35:00,002-ERROR-[文件名][函数名_002] 错误信息');
```

#### 6. 响应式适配
```css
@media screen and (max-width: 400px) {
  .header-section {
    padding: 15rpx 20rpx;
  }
  
  .main-title {
    font-size: 32rpx;
  }
}
```

### 功能模块说明

#### 舌诊引导页面 (guide.vue)
- 功能：介绍舌诊概念和拍摄指导
- 特点：渐变背景，卡片式布局，步骤化指导

#### 舌苔拍摄页面 (photo-upload.vue)
- 功能：相机拍摄、相册选择、拍摄引导
- 特点：简洁布局，竖立拍摄框，权限管理
- 拍摄框：240rpx × 320rpx 竖立长方形

## 问题解决思路和方法标准

### 代码修改标准流程
1. 明确用户需求，如果需要任何信息，请主动询问用户
2. 制定实现方案，清楚地描述和解释推理和计划
3. 先使用命令罗列仓库中的文件，查看有哪些文件涉及到此次修改，然后逐一更新代码
4. 为每一行代码提供注释，要求意义清晰，明确
5. 首先思考导致问题的5-7个可能原因，然后从中提炼出1-2个最可能导致问题的原因，先添加日志来验证假设，再修改代码修复问题

### UniApp微信小程序编译错误解决方案

#### 问题类型：wxml编译错误 - data-event-opts属性语法错误

**错误信息示例：**
```
Error: wxml 编译错误，错误信息：./pagesExt/tuozhanyuan/apply.wxml:1:3451: Bad attr `data-event-opts` with message: unexpected token `condition`.
```

**问题原因分析：**
1. Vue模板中使用了过于复杂的表达式，在编译为微信小程序时出现语法错误
2. v-for循环中的key值使用了复杂的字符串拼接表达式
3. 事件绑定中包含了复杂的对象引用和计算属性

**解决方案：**
1. **简化Vue模板结构**
   - 避免在模板中使用复杂的计算表达式
   - 将复杂逻辑移到methods或computed中处理
   - 使用简单的数据绑定替代复杂的表达式

2. **重构数据结构**
   ```javascript
   // 原来的复杂结构
   v-for="(condition, index) in selectedLevel.applyConditions" :key="'condition' + index"

   // 简化后的结构
   v-for="(condition, index) in applyConditionsList" :key="index"
   ```

3. **分离显示逻辑**
   ```javascript
   // 添加独立的显示控制变量
   data() {
     return {
       showApplyConditions: false,
       showUpgradeConditions: false,
       applyConditionsList: [],
       upgradeConditionsList: []
     }
   }

   // 添加更新显示状态的方法
   updateConditionsDisplay: function() {
     if (this.selectedLevel) {
       this.showApplyConditions = !!(this.selectedLevel.applyConditions && this.selectedLevel.applyConditions.length > 0);
       this.applyConditionsList = this.selectedLevel.applyConditions || [];
     }
   }
   ```

4. **统一CSS类名规范**
   - 使用下划线命名：`apply_box`, `apply_item`
   - 保持与现有页面（如business/apply.vue）的命名一致性

**预防措施：**
1. 在Vue模板中避免使用复杂的JavaScript表达式
2. 优先使用computed属性处理复杂的数据计算
3. 保持代码结构与现有页面的一致性
4. 定期测试编译结果，及早发现问题